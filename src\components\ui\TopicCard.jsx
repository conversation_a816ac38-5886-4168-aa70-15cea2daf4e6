import React, { useState } from 'react';
import ResourcesModal from './ResourcesModal';
import AudioModal from './AudioModal';

const TopicCard = ({ topic, onEdit, onDelete, onResourcesUpdate }) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isAudioModalOpen, setIsAudioModalOpen] = useState(false);
  const [selectedPdfIndex, setSelectedPdfIndex] = useState(null);
  const [playingAudio, setPlayingAudio] = useState(null);

  const toggleExpand = () => setIsExpanded(!isExpanded);

  const handleAddResources = () => {
    setIsModalOpen(true);
  };

  const handleModalClose = () => {
    setIsModalOpen(false);
  };

  const handleResourcesSave = (resources) => {
    // Call parent component to update resources
    if (onResourcesUpdate) {
      onResourcesUpdate(topic.id, resources);
    }
    setIsModalOpen(false);
  };

  const handleAddAudioToPdf = (pdfIndex) => {
    setSelectedPdfIndex(pdfIndex);
    setIsAudioModalOpen(true);
  };

  const handleAudioModalClose = () => {
    setIsAudioModalOpen(false);
    setSelectedPdfIndex(null);
  };

  const handleAudioSave = (audioFiles) => {
    if (onResourcesUpdate && selectedPdfIndex !== null) {
      onResourcesUpdate(topic.id, null, selectedPdfIndex, audioFiles);
    }
    setIsAudioModalOpen(false);
    setSelectedPdfIndex(null);
  };

  const handleAudioPlay = (audioId) => {
    if (playingAudio === audioId) {
      setPlayingAudio(null);
    } else {
      setPlayingAudio(audioId);
    }
  };

  const handleDownload = (fileUrl, fileName) => {
    // Implement download functionality
    console.log(`Downloading ${fileName} from ${fileUrl}`);
  };

  const handlePreview = (fileUrl, fileName) => {
    // Implement preview functionality
    console.log(`Previewing ${fileName} from ${fileUrl}`);
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDuration = (seconds) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const statusClassName = {
    active: 'status-active',
    draft: 'status-draft',
    inactive: 'status-inactive',
  }[topic.status] || 'status-draft';

  return (
    <>
      <div className="topic-card">
        <div className="topic-card-header" onClick={toggleExpand}>
          <div className="topic-card-title">
            <h3>{topic.title}</h3>
            <p>{topic.description}</p>
          </div>
          <div className="topic-card-actions">
            <span className={`status-badge ${statusClassName}`}>{topic.status}</span>
            <button className="topic-edit-btn" onClick={(e) => { e.stopPropagation(); onEdit(topic.id); }}>
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"/>
                <path d="m18.5 2.5 3 3L12 15l-4 1 1-4 9.5-9.5z"/>
              </svg>
            </button>
            <button className="topic-delete-btn" onClick={(e) => { e.stopPropagation(); onDelete(topic.id); }}>
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <polyline points="3,6 5,6 21,6"/>
                <path d="m19,6v14a2,2 0 0,1 -2,2H7a2,2 0 0,1 -2,-2V6m3,0V4a2,2 0 0,1 2,-2h4a2,2 0 0,1 2,2v2"/>
                <line x1="10" y1="11" x2="10" y2="17"/>
                <line x1="14" y1="11" x2="14" y2="17"/>
              </svg>
            </button>
            <button className="topic-expand-btn">
              <svg className={`expand-icon ${isExpanded ? 'expanded' : ''}`} width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <polyline points="6,9 12,15 18,9"/>
              </svg>
            </button>
          </div>
        </div>

        <div className={`topic-card-body ${isExpanded ? 'expanded' : ''}`}>
          <div className="resource-section">
            <h4>PDF Resources</h4>
            {topic.resources && topic.resources.pdfs && topic.resources.pdfs.length > 0 ? (
              topic.resources.pdfs.map((pdf, pdfIndex) => (
                <div key={pdfIndex} className="pdf-resource-container">
                  <div className="pdf-resource-item">
                    <div className="resource-icon">
                      <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2Z"/>
                        <polyline points="14,2 14,8 20,8"/>
                        <line x1="16" y1="13" x2="8" y2="13"/>
                        <line x1="16" y1="17" x2="8" y2="17"/>
                        <polyline points="10,9 9,9 8,9"/>
                      </svg>
                    </div>
                    <div className="resource-info">
                      <div className="resource-name">{pdf.title || pdf.name}</div>
                      <div className="resource-meta">
                        {pdf.filename} • {formatFileSize(pdf.size)} • {new Date(pdf.uploadDate).toLocaleDateString()}
                      </div>
                    </div>
                    <div className="resource-actions">
                      <button
                        className="resource-action-btn"
                        onClick={() => handlePreview(pdf.url, pdf.filename)}
                        title="Preview PDF"
                      >
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
                          <circle cx="12" cy="12" r="3"/>
                        </svg>
                      </button>
                      <button
                        className="resource-action-btn"
                        onClick={() => handleDownload(pdf.url, pdf.filename)}
                        title="Download PDF"
                      >
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                          <polyline points="7,10 12,15 17,10"/>
                          <line x1="12" y1="15" x2="12" y2="3"/>
                        </svg>
                      </button>
                      <button
                        className="resource-action-btn add-audio-btn"
                        onClick={() => handleAddAudioToPdf(pdfIndex)}
                        title="Add Audio Files"
                      >
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <circle cx="12" cy="12" r="10"/>
                          <line x1="12" y1="8" x2="12" y2="16"/>
                          <line x1="8" y1="12" x2="16" y2="12"/>
                        </svg>
                      </button>
                    </div>
                  </div>

                  {/* Nested Audio Files */}
                  <div className="audio-files-container">
                    {pdf.audioFiles && pdf.audioFiles.length > 0 ? (
                      pdf.audioFiles.map((audio, audioIndex) => (
                        <div key={audioIndex} className="audio-resource-item">
                          <div className="resource-icon audio-icon">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                              <polygon points="11 5 6 9 2 9 2 15 6 15 11 19 11 5"/>
                              <path d="M19.07 4.93a10 10 0 0 1 0 14.14M15.54 8.46a5 5 0 0 1 0 7.07"/>
                            </svg>
                          </div>
                          <div className="resource-info">
                            <div className="resource-name">{audio.title || audio.name}</div>
                            <div className="resource-meta">
                              {audio.filename} • {formatFileSize(audio.size)} • {formatDuration(audio.duration || 0)}
                            </div>
                          </div>
                          <div className="resource-actions">
                            <button
                              className={`resource-action-btn audio-play-btn ${playingAudio === `${pdfIndex}-${audioIndex}` ? 'playing' : ''}`}
                              onClick={() => handleAudioPlay(`${pdfIndex}-${audioIndex}`)}
                              title={playingAudio === `${pdfIndex}-${audioIndex}` ? "Pause" : "Play"}
                            >
                              {playingAudio === `${pdfIndex}-${audioIndex}` ? (
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                  <rect x="6" y="4" width="4" height="16"/>
                                  <rect x="14" y="4" width="4" height="16"/>
                                </svg>
                              ) : (
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                  <polygon points="5,3 19,12 5,21 5,3"/>
                                </svg>
                              )}
                            </button>
                            <button
                              className="resource-action-btn"
                              onClick={() => handleDownload(audio.url, audio.filename)}
                              title="Download Audio"
                            >
                              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"/>
                                <polyline points="7,10 12,15 17,10"/>
                                <line x1="12" y1="15" x2="12" y2="3"/>
                              </svg>
                            </button>
                          </div>
                        </div>
                      ))
                    ) : (
                      <div className="no-audio-message">No audio files attached</div>
                    )}
                  </div>
                </div>
              ))
            ) : (
              <div className="no-resources-message">No PDF resources available.</div>
            )}

            <button className="add-resources-btn" onClick={handleAddResources}>
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <circle cx="12" cy="12" r="10"/>
                <line x1="12" y1="8" x2="12" y2="16"/>
                <line x1="8" y1="12" x2="16" y2="12"/>
              </svg>
              Add Resources
            </button>
          </div>
        </div>
      </div>

      <ResourcesModal
        isOpen={isModalOpen}
        onClose={handleModalClose}
        onSave={handleResourcesSave}
        topicTitle={topic.title}
      />

      <AudioModal
        isOpen={isAudioModalOpen}
        onClose={handleAudioModalClose}
        onSave={handleAudioSave}
        pdfTitle={selectedPdfIndex !== null && topic.resources?.pdfs?.[selectedPdfIndex]
          ? topic.resources.pdfs[selectedPdfIndex].title || topic.resources.pdfs[selectedPdfIndex].filename
          : 'PDF'
        }
      />
    </>
  );
};

export default TopicCard;