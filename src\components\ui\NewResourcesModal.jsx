import React, { useState } from 'react';
import Modal from 'react-modal';
import '../../styles/Topics.css';

// Helper component for file display
const FilePreview = ({ file, onClear, type }) => {
  if (!file) return <span className="file-name-placeholder">No file selected</span>;
  
  const getFileIcon = () => {
    if (type === 'pdf') {
      return (
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
          <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2Z"/>
          <polyline points="14,2 14,8 20,8"/>
        </svg>
      );
    } else {
      return (
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
          <polygon points="11 5 6 9 2 9 2 15 6 15 11 19 11 5"/>
          <path d="M19.07 4.93a10 10 0 0 1 0 14.14M15.54 8.46a5 5 0 0 1 0 7.07"/>
        </svg>
      );
    }
  };

  const handleRemoveClick = (e) => {
    e.preventDefault();
    e.stopPropagation();
    if (onClear) {
      onClear();
    }
  };

  return (
    <div className="file-preview">
      <div className="file-preview-icon">
        {getFileIcon()}
      </div>
      <span className="file-name">{file.name}</span>
      <button onClick={handleRemoveClick} className="file-clear-btn" title="Clear selection">
        ×
      </button>
    </div>
  );
};

const NewResourcesModal = ({ isOpen, onClose, onSave, topicTitle, existingPdfs = [] }) => {
  // State for resource management
  const [resourceType, setResourceType] = useState('PDF');
  const [selectedFile, setSelectedFile] = useState(null);
  const [resourceTitle, setResourceTitle] = useState('');
  const [selectedParentPdf, setSelectedParentPdf] = useState('');
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});

  const handleFileChange = (file) => {
    setSelectedFile(file);
    // Auto-populate title if empty
    if (!resourceTitle && file) {
      const fileName = file.name.replace(/\.[^/.]+$/, ''); // Remove extension
      setResourceTitle(fileName);
    }
    // Clear any existing errors
    if (errors.file) {
      setErrors(prev => ({ ...prev, file: null }));
    }
  };

  const handleClearFile = () => {
    const fileInput = document.getElementById('resource-file-input');
    if (fileInput) fileInput.value = '';
    setSelectedFile(null);
  };

  const handleResourceTypeChange = (type) => {
    setResourceType(type);
    setSelectedFile(null);
    setResourceTitle('');
    setSelectedParentPdf('');
    setErrors({});
    // Clear file input
    const fileInput = document.getElementById('resource-file-input');
    if (fileInput) fileInput.value = '';
  };

  const validateResource = () => {
    const newErrors = {};
    let hasErrors = false;

    // Check if file is selected
    if (!selectedFile) {
      newErrors.file = 'Please select a file';
      hasErrors = true;
    } else {
      // Validate file type based on resource type
      if (resourceType === 'PDF' && selectedFile.type !== 'application/pdf') {
        newErrors.file = 'Please select a valid PDF file';
        hasErrors = true;
      } else if (resourceType === 'Audio' && !selectedFile.type.startsWith('audio/')) {
        newErrors.file = 'Please select a valid audio file';
        hasErrors = true;
      }

      // Validate file size
      const maxSize = resourceType === 'PDF' ? 10 * 1024 * 1024 : 50 * 1024 * 1024; // 10MB for PDF, 50MB for Audio
      if (selectedFile.size > maxSize) {
        const maxSizeText = resourceType === 'PDF' ? '10MB' : '50MB';
        newErrors.file = `File size must be less than ${maxSizeText}`;
        hasErrors = true;
      }
    }

    setErrors(newErrors);
    return !hasErrors;
  };

  const handleSave = async () => {
    if (!validateResource()) {
      return;
    }

    setLoading(true);
    try {
      // Create resource object based on type
      const resource = {
        type: resourceType,
        title: resourceTitle || selectedFile.name.replace(/\.[^/.]+$/, ''),
        filename: selectedFile.name,
        size: selectedFile.size,
        uploadDate: new Date().toISOString(),
        url: URL.createObjectURL(selectedFile), // Temporary URL for preview
      };

      // Add parent PDF ID for audio resources
      if (resourceType === 'Audio' && selectedParentPdf) {
        resource.parentPdfId = selectedParentPdf;
      }

      await onSave(resource);
      onClose();
      
      // Reset form
      setResourceType('PDF');
      setSelectedFile(null);
      setResourceTitle('');
      setSelectedParentPdf('');
      setErrors({});
    } catch (error) {
      setErrors({ general: 'Failed to save resource. Please try again.' });
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    onClose();
    // Reset form
    setResourceType('PDF');
    setSelectedFile(null);
    setResourceTitle('');
    setSelectedParentPdf('');
    setErrors({});
  };

  return (
    <Modal
      isOpen={isOpen}
      onRequestClose={handleClose}
      className="resources-modal"
      overlayClassName="resources-modal-overlay"
      ariaHideApp={false}
    >
      <div className="modal-header">
        <h2 className="modal-title">Add Resource to "{topicTitle}"</h2>
        <button onClick={handleClose} className="modal-close-btn">×</button>
      </div>

      <div className="modal-body">
        {errors.general && (
          <div className="error-message general-error">
            {errors.general}
          </div>
        )}
        
        {/* Resource Type Selection */}
        <div className="resource-type-section">
          <label htmlFor="resource-type-select">Resource Type *</label>
          <select
            id="resource-type-select"
            className="resource-type-select"
            value={resourceType}
            onChange={(e) => handleResourceTypeChange(e.target.value)}
          >
            <option value="PDF">PDF Document</option>
            <option value="Audio">Audio File</option>
          </select>
        </div>

        {/* File Upload Section */}
        <div className="file-input-group">
          <label htmlFor="resource-file-input">
            {resourceType === 'PDF' ? 'PDF File *' : 'Audio File *'}
          </label>
          <div className="file-input-wrapper">
            <input
              type="file"
              id="resource-file-input"
              className="file-input"
              accept={resourceType === 'PDF' ? '.pdf' : 'audio/*'}
              onChange={(e) => handleFileChange(e.target.files[0])}
            />
            <label htmlFor="resource-file-input" className="file-input-label">
              {selectedFile ? (
                <FilePreview
                  file={selectedFile}
                  onClear={handleClearFile}
                  type={resourceType.toLowerCase()}
                />
              ) : (
                <div className="file-drop-zone-small">
                  <div className="drop-zone-icon-small">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <circle cx="12" cy="12" r="10"/>
                      <line x1="12" y1="8" x2="12" y2="16"/>
                      <line x1="8" y1="12" x2="16" y2="12"/>
                    </svg>
                  </div>
                  <span className="file-name-placeholder">
                    Click to select {resourceType === 'PDF' ? 'PDF' : 'audio'} file
                  </span>
                </div>
              )}
            </label>
          </div>
          {errors.file && (
            <div className="error-message">
              {errors.file}
            </div>
          )}
        </div>

        {/* Title Input */}
        <div className="file-input-group">
          <label htmlFor="resource-title-input">Resource Title (Optional)</label>
          <input
            type="text"
            id="resource-title-input"
            className="text-input"
            placeholder={`Enter a custom title for this ${resourceType.toLowerCase()}`}
            value={resourceTitle}
            onChange={(e) => setResourceTitle(e.target.value)}
          />
          <div className="help-text">
            If left empty, the filename will be used as the title
          </div>
        </div>

        {/* Parent PDF Selection for Audio Resources */}
        {resourceType === 'Audio' && (
          <div className="file-input-group">
            <label htmlFor="parent-pdf-select">Associate with PDF (Optional)</label>
            <select
              id="parent-pdf-select"
              className="parent-pdf-select"
              value={selectedParentPdf}
              onChange={(e) => setSelectedParentPdf(e.target.value)}
            >
              <option value="">Standalone audio (no parent PDF)</option>
              {existingPdfs.map((pdf) => (
                <option key={pdf.id} value={pdf.id}>
                  {pdf.title || pdf.filename}
                </option>
              ))}
            </select>
            <div className="help-text">
              {existingPdfs.length === 0 
                ? 'No PDFs available. Upload a PDF first to associate audio files with it.'
                : 'Select a PDF to nest this audio file under it, or leave blank for standalone audio.'
              }
            </div>
          </div>
        )}
      </div>

      <div className="modal-footer">
        <button onClick={handleClose} className="btn btn-secondary" disabled={loading}>
          Cancel
        </button>
        <button 
          onClick={handleSave} 
          className="btn btn-primary" 
          disabled={loading || !selectedFile}
        >
          {loading ? 'Saving...' : `Add ${resourceType}`}
        </button>
      </div>
    </Modal>
  );
};

export default NewResourcesModal;
