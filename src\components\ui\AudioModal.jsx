import React, { useState } from 'react';
import Modal from 'react-modal';
import '../../styles/Topics.css';

const AudioModal = ({ isOpen, onClose, onSave, pdfTitle }) => {
  const [audioFiles, setAudioFiles] = useState([]);
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});

  const handleAudioFilesChange = (files) => {
    const audioFilesArray = Array.from(files);
    setAudioFiles(audioFilesArray);
    // Clear any existing errors
    if (errors.audio) {
      setErrors(prev => ({ ...prev, audio: null }));
    }
  };

  const handleClearAudio = () => {
    const fileInput = document.getElementById('audio-files-input');
    if (fileInput) fileInput.value = '';
    setAudioFiles([]);
  };

  const validateFiles = () => {
    const newErrors = {};
    let hasErrors = false;

    if (audioFiles.length === 0) {
      newErrors.audio = 'Please select at least one audio file';
      hasErrors = true;
    } else {
      audioFiles.forEach((audio, index) => {
        if (!audio.type.startsWith('audio/')) {
          newErrors.audio = 'Please select valid audio files only';
          hasErrors = true;
        }
        // Validate audio file size (50MB limit)
        else if (audio.size > 50 * 1024 * 1024) {
          newErrors.audio = 'Audio file size must be less than 50MB each';
          hasErrors = true;
        }
      });
    }

    setErrors(newErrors);
    return !hasErrors;
  };

  const handleSave = async () => {
    if (!validateFiles()) {
      return;
    }

    setLoading(true);
    try {
      // Transform data to match expected structure
      const formattedAudioFiles = audioFiles.map(audio => ({
        title: audio.name.replace(/\.[^/.]+$/, ''),
        filename: audio.name,
        size: audio.size,
        duration: 0, // Would be calculated on server
        url: URL.createObjectURL(audio) // Temporary URL for preview
      }));

      await onSave(formattedAudioFiles);
      onClose();

      // Reset form
      setAudioFiles([]);
      setErrors({});
    } catch (error) {
      setErrors({ general: 'Failed to add audio files. Please try again.' });
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    onClose();
    // Reset form
    setAudioFiles([]);
    setErrors({});
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <Modal
      isOpen={isOpen}
      onRequestClose={handleClose}
      className="resources-modal audio-modal"
      overlayClassName="resources-modal-overlay"
      ariaHideApp={false}
    >
      <div className="modal-header">
        <h2 className="modal-title">Add Audio Files to "{pdfTitle}"</h2>
        <button onClick={handleClose} className="modal-close-btn">×</button>
      </div>

      <div className="modal-body">
        {errors.general && (
          <div className="error-message general-error">
            {errors.general}
          </div>
        )}

        <div className="audio-upload-section">
          <div className="file-input-group">
            <label htmlFor="audio-files-input">Select Audio Files *</label>
            <div className="file-input-wrapper">
              <input
                type="file"
                id="audio-files-input"
                className="file-input"
                accept="audio/*"
                multiple
                onChange={(e) => handleAudioFilesChange(e.target.files)}
              />
              <label htmlFor="audio-files-input" className="file-input-label">
                <div className="audio-files-preview-container">
                {audioFiles.length > 0 ? (
                  <div className="selected-audio-files">
                    <div className="audio-files-header">
                      <span className="files-count">{audioFiles.length} file(s) selected</span>
                      <button
                        onClick={handleClearAudio}
                        className="clear-all-btn"
                        title="Clear all files"
                      >
                        Clear All
                      </button>
                    </div>
                    <div className="audio-files-list">
                      {audioFiles.map((audio, index) => (
                        <div key={index} className="audio-file-item">
                          <div className="audio-file-icon">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                              <polygon points="11 5 6 9 2 9 2 15 6 15 11 19 11 5"/>
                              <path d="M19.07 4.93a10 10 0 0 1 0 14.14M15.54 8.46a5 5 0 0 1 0 7.07"/>
                            </svg>
                          </div>
                          <div className="audio-file-info">
                            <div className="audio-file-name">{audio.name}</div>
                            <div className="audio-file-size">{formatFileSize(audio.size)}</div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                ) : (
                  <div className="file-drop-zone">
                    <div className="drop-zone-icon">
                      <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <circle cx="12" cy="12" r="10"/>
                        <line x1="12" y1="8" x2="12" y2="16"/>
                        <line x1="8" y1="12" x2="16" y2="12"/>
                      </svg>
                    </div>
                    <div className="drop-zone-text">
                      <p><strong>Click to select audio files</strong></p>
                      <p>or drag and drop them here</p>
                      <p className="file-types">Supported: MP3, WAV, M4A, OGG</p>
                    </div>
                  </div>
                )}
                </div>
              </label>
            </div>
            {errors.audio && (
              <div className="error-message">
                {errors.audio}
              </div>
            )}
            <div className="help-text">
              You can select multiple audio files at once. Each file should be less than 50MB.
            </div>
          </div>
        </div>
      </div>

      <div className="modal-footer">
        <button onClick={handleClose} className="btn btn-secondary" disabled={loading}>
          Cancel
        </button>
        <button
          onClick={handleSave}
          className="btn btn-primary"
          disabled={loading || audioFiles.length === 0}
        >
          {loading ? 'Adding...' : `Add ${audioFiles.length} Audio File${audioFiles.length !== 1 ? 's' : ''}`}
        </button>
      </div>
    </Modal>
  );
};

export default AudioModal;
