/* Topics Page Specific Styles - Admin Portal Design */

/* Page Layout */
.topics-page-container {
  background-color: #FFFBF3;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Header Styles */
.topics-header-card {
  background-color: #FFF8EB;
  border: 1px solid #e0e0e0;
  border-top: none;
  border-radius: 0 0 12px 12px;
  box-shadow: none;
  padding: 24px 40px;
  text-align: center;
  margin: 0;
}

.topics-page-title {
  font-size: 32px;
  font-weight: 600;
  color: #333;
  margin: 0;
  line-height: 1.2;
}

/* Controls Section */
.topics-controls {
  padding: 0;
  margin: 32px 0;
  max-width: 1400px;
  width: 100%;
  margin-left: auto;
  margin-right: auto;
}

.topics-controls-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 20px;
}

/* Breadcrumb Styles */
.topics-breadcrumb {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  color: #666;
}

.topics-breadcrumb span {
  cursor: pointer;
  transition: color 0.2s ease;
}

.topics-breadcrumb span:first-child:hover {
  color: #333;
  text-decoration: underline;
}

.breadcrumb-separator {
  color: #999;
  cursor: default !important;
}

.breadcrumb-separator:hover {
  color: #999 !important;
  text-decoration: none !important;
}

/* Add Topic Button */
.topics-add-btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  background-color: #374151;
  color: #ffffff;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
}

.topics-add-btn:hover {
  background-color: #4B5563;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Topic Cards Container */
.topics-list-container {
  max-width: 1400px;
  width: 100%;
  margin: 0 auto;
  padding: 0;
}

/* Topic Card Styles */
.topic-card {
  background-color: #FFF8EB;
  border-radius: 12px;
  border: 1px solid #e0e0e0;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;
}

.topic-card:hover {
  border-color: #c0c0c0;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.topic-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  cursor: pointer;
}

.topic-card-title h3 {
  font-size: 20px;
  font-weight: 600;
  margin: 0 0 4px 0;
  color: #333;
}

.topic-card-title p {
  font-size: 14px;
  color: #666;
  margin: 0;
}

.topic-card-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

/* Status Badge Styles */
.status-badge {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-active {
  background-color: #D4F4DD;
  color: #0F5132;
}

.status-draft {
  background-color: #FFF3CD;
  color: #664D03;
}

.status-inactive {
  background-color: #F8D7DA;
  color: #721C24;
}

/* Action Buttons */
.topic-edit-btn,
.topic-delete-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  background-color: #ffffff;
  cursor: pointer;
  transition: all 0.2s ease;
}

.topic-edit-btn:hover {
  background-color: #f8f9fa;
  border-color: #6c757d;
}

.topic-delete-btn:hover {
  background-color: #f8f9fa;
  border-color: #dc3545;
}

.topic-expand-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
}

.expand-icon {
  transition: transform 0.3s ease;
  color: #666;
}

.expand-icon.expanded {
  transform: rotate(180deg);
}

/* Topic Card Body */
.topic-card-body {
  padding: 0 24px 24px 24px;
  border-top: 1px solid #e0e0e0;
  background-color: #FFFBF3;
  border-radius: 0 0 12px 12px;
}

.resource-section {
  margin-top: 20px;
}

.resource-section h4 {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 12px;
  color: #333;
}

.resource-item {
  display: flex;
  align-items: center;
  padding: 12px;
  border-radius: 8px;
  background-color: #FFF8EB;
  margin-bottom: 8px;
  border: 1px solid #f5f5f5;
}

.add-resource-btn {
  margin-top: 12px;
  background-color: #f8f9fa;
  color: #374151;
  border: 1px solid #e0e0e0;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.add-resource-btn:hover {
  background-color: #e9ecef;
  border-color: #6c757d;
}


.resources-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.resources-modal {
  background: #ffffff;
  border-radius: 12px;
  width: 90vw;
  max-width: 700px;
  max-height: 90vh;
  box-shadow: 0 5px 25px rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
  overflow: hidden; /* Prevent content from spilling */
}

.modal-header {
  padding: 16px 24px;
  border-bottom: 1px solid #e0e0e0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-title {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.modal-close-btn {
  background: none;
  border: none;
  font-size: 28px;
  line-height: 1;
  color: #666;
  cursor: pointer;
}

.modal-body {
  padding: 24px;
  overflow-y: auto; /* Make body scrollable */
  flex-grow: 1;
}

.resource-upload-row {
  display: flex;
  align-items: flex-end;
  gap: 16px;
  padding-bottom: 24px;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 24px;
}

.resource-upload-row:last-child {
  border-bottom: none;
  margin-bottom: 0;
  padding-bottom: 0;
}

.remove-row-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    background-color: #f8d7da;
    color: #721c24;
    cursor: pointer;
    margin-bottom: 6px; /* Align with file preview */
}

.remove-row-btn:hover {
  background-color: #f1c6cb;
}


.file-input-group {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.file-input-group label {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 8px;
}

.file-input-wrapper {
  position: relative;
  width: 100%;
  height: 38px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  background-color: #f8f9fa;
  overflow: hidden;
}

.file-input {
  position: absolute;
  opacity: 0;
  width: 100%;
  height: 100%;
  cursor: pointer;
  z-index: 2;
}

.file-preview {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 12px;
  height: 100%;
  font-size: 14px;
  color: #333;
}

.file-name {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding-right: 8px;
}

.file-name-placeholder {
  color: #666;
  font-style: italic;
  padding: 0 12px;
  line-height: 36px;
}

.file-clear-btn {
  background: none;
  border: none;
  font-size: 18px;
  color: #666;
  cursor: pointer;
  z-index: 3;
}

.add-more-btn {
  background-color: transparent;
  border: 1px dashed #6c757d;
  color: #6c757d;
  border-radius: 6px;
  padding: 10px 16px;
  cursor: pointer;
  font-weight: 500;
  width: 100%;
  margin-top: 16px;
}

.add-more-btn:hover {
  background-color: #f8f9fa;
  color: #333;
  border-color: #333;
}


.modal-footer {
  padding: 16px 24px;
  border-top: 1px solid #e0e0e0;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  background-color: #f8f9fa;
}

.btn {
  padding: 10px 20px;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  border: 1px solid transparent;
}

.btn-secondary {
  background-color: #fff;
  border-color: #e0e0e0;
  color: #333;
}

.btn-secondary:hover {
  background-color: #f8f9fa;
}

.btn-primary {
  background-color: #374151;
  color: #ffffff;
}

.btn-primary:hover {
  background-color: #4B5563;
}


/*
/* STYLES FROM INLINE TO CSS
/*-------------------------------------------------- */

.resource-icon {
  margin-right: 12px;
  flex-shrink: 0; /* Prevents icon from shrinking */
}

.pdf-icon {
  color: #dc3545; /* Red for PDFs */
}

.audio-icon {
  color: #28a745; /* Green for Audio */
}

.resource-details {
  flex: 1;
  min-width: 0; /* Allows text truncation to work correctly in a flex container */
}

.resource-name {
  font-weight: 500;
  font-size: 14px;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.resource-meta {
  font-size: 12px;
  color: #666;
}


/* Responsive Design - Admin Portal Breakpoints */
@media (max-width: 1024px) {
  .topics-header-card {
    padding: 20px 24px;
  }

  .topics-controls,
  .topics-list-container {
    padding-left: 0;
    padding-right: 0;
    margin: 24px 0;
  }

  .topics-page-title {
    font-size: 28px;
  }

  .topics-add-btn {
    padding: 10px 20px;
    font-size: 13px;
  }
}

@media (max-width: 768px) {
  .topics-header-card {
    padding: 16px 20px;
  }

  .topics-controls,
  .topics-list-container {
    padding-left: 0;
    padding-right: 0;
    margin: 20px 0;
  }

  .topics-page-title {
    font-size: 24px;
  }

  .topics-controls-row {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .topics-breadcrumb {
    text-align: center;
  }

  .topics-add-btn {
    width: 100%;
    justify-content: center;
  }

  .topic-card-header {
    padding: 16px 20px;
  }

  .topic-card-body {
    padding: 0 20px 20px 20px;
  }

  .topic-card-actions {
    gap: 12px;
  }
}

@media (max-width: 576px) {
  .topics-header-card {
    padding: 12px 16px;
  }

  .topics-controls,
  .topics-list-container {
    padding-left: 0;
    padding-right: 0;
    margin: 16px 0;
  }

  .topics-page-title {
    font-size: 20px;
  }

  .topics-breadcrumb span {
    font-size: 14px;
  }

  .topics-add-btn {
    padding: 10px 16px;
    font-size: 12px;
  }

  .topic-card-header {
    padding: 14px 16px;
  }

  .topic-card-body {
    padding: 0 16px 16px 16px;
  }

  .topic-card-title h3 {
    font-size: 18px;
  }

  .topic-edit-btn,
  .topic-delete-btn {
    width: 28px;
    height: 28px;
  }

  .topic-card-actions {
    gap: 8px;
  }
}