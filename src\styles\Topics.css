/* Topics Page Specific Styles - Admin Portal Design */

/* Page Layout */
.topics-page-container {
  background-color: #FFFBF3;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* Header Styles */
.topics-header-card {
  background-color: #FFF8EB;
  border: 1px solid #e0e0e0;
  border-top: none;
  border-radius: 0 0 12px 12px;
  box-shadow: none;
  padding: 24px 40px;
  text-align: center;
  margin: 0;
}

.topics-page-title {
  font-size: 32px;
  font-weight: 600;
  color: #333;
  margin: 0;
  line-height: 1.2;
}

/* Controls Section */
.topics-controls {
  padding: 0;
  margin: 32px 0;
  max-width: 1400px;
  width: 100%;
  margin-left: auto;
  margin-right: auto;
}

.topics-controls-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 20px;
}

/* Breadcrumb Styles */
.topics-breadcrumb {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  color: #666;
}

.topics-breadcrumb span {
  cursor: pointer;
  transition: color 0.2s ease;
}

.topics-breadcrumb span:first-child:hover {
  color: #333;
  text-decoration: underline;
}

.breadcrumb-separator {
  color: #999;
  cursor: default !important;
}

.breadcrumb-separator:hover {
  color: #999 !important;
  text-decoration: none !important;
}

/* Add Topic Button */
.topics-add-btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  background-color: #374151;
  color: #ffffff;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
}

.topics-add-btn:hover {
  background-color: #4B5563;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Topic Cards Container */
.topics-list-container {
  max-width: 1400px;
  width: 100%;
  margin: 0 auto;
  padding: 0;
}

/* Topic Card Styles */
.topic-card {
  background-color: #FFF8EB;
  border-radius: 12px;
  border: 1px solid #e0e0e0;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;
}

.topic-card:hover {
  border-color: #c0c0c0;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.topic-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  cursor: pointer;
}

.topic-card-title h3 {
  font-size: 20px;
  font-weight: 600;
  margin: 0 0 4px 0;
  color: #333;
}

.topic-card-title p {
  font-size: 14px;
  color: #666;
  margin: 0;
}

.topic-card-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

/* Status Badge Styles */
.status-badge {
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-active {
  background-color: #D4F4DD;
  color: #0F5132;
}

.status-draft {
  background-color: #FFF3CD;
  color: #664D03;
}

.status-inactive {
  background-color: #F8D7DA;
  color: #721C24;
}

/* Action Buttons */
.topic-edit-btn,
.topic-delete-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  background-color: #ffffff;
  cursor: pointer;
  transition: all 0.2s ease;
}

.topic-edit-btn:hover {
  background-color: #f8f9fa;
  border-color: #6c757d;
}

.topic-delete-btn:hover {
  background-color: #f8f9fa;
  border-color: #dc3545;
}

.topic-expand-btn {
  background: none;
  border: none;
  cursor: pointer;
  padding: 4px;
}

.expand-icon {
  transition: transform 0.3s ease;
  color: #666;
}

.expand-icon.expanded {
  transform: rotate(180deg);
}

/* Topic Card Body */
.topic-card-body {
  max-height: 0;
  overflow: hidden;
  padding: 0 24px;
  border-top: 1px solid #e0e0e0;
  background-color: #FFFBF3;
  border-radius: 0 0 12px 12px;
  transition: max-height 0.3s ease, padding 0.3s ease;
}

.topic-card-body.expanded {
  max-height: 1000px;
  padding: 20px 24px 24px 24px;
}

.resource-section {
  margin-top: 0;
}

.resource-section h4 {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 16px;
  color: #333;
}

/* PDF Resource Container */
.pdf-resource-container {
  margin-bottom: 16px;
  border-radius: 8px;
  border: 1px solid #e8e8e8;
  background-color: #ffffff;
  overflow: hidden;
}

.pdf-resource-item {
  display: flex;
  align-items: center;
  padding: 16px;
  background-color: #FFF8EB;
  border-bottom: 1px solid #f0f0f0;
}

.resource-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border-radius: 8px;
  background-color: #ffffff;
  border: 1px solid #e0e0e0;
  margin-right: 12px;
  flex-shrink: 0;
}

.resource-icon svg {
  color: #dc3545;
}

.audio-icon svg {
  color: #28a745;
}

.resource-info {
  flex: 1;
  min-width: 0;
}

.resource-name {
  font-weight: 500;
  font-size: 14px;
  color: #333;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.resource-meta {
  font-size: 12px;
  color: #666;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.resource-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-left: 12px;
}

.resource-action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  background-color: #ffffff;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #666;
}

.resource-action-btn:hover {
  background-color: #f8f9fa;
  border-color: #6c757d;
  color: #333;
}

.audio-play-btn.playing {
  background-color: #28a745;
  border-color: #28a745;
  color: #ffffff;
}

.audio-play-btn.playing:hover {
  background-color: #218838;
  border-color: #1e7e34;
}

/* Audio Files Container */
.audio-files-container {
  background-color: #FFFBF3;
}

.audio-resource-item {
  display: flex;
  align-items: center;
  padding: 12px 16px 12px 32px;
  border-bottom: 1px solid #f5f5f5;
  position: relative;
}

.audio-resource-item:last-child {
  border-bottom: none;
}

.audio-resource-item::before {
  content: '';
  position: absolute;
  left: 20px;
  top: 0;
  bottom: 0;
  width: 2px;
  background-color: #e0e0e0;
}

.audio-resource-item .resource-icon {
  width: 32px;
  height: 32px;
  margin-right: 10px;
}

.no-audio-message {
  padding: 12px 16px 12px 32px;
  font-size: 13px;
  color: #999;
  font-style: italic;
  border-bottom: 1px solid #f5f5f5;
}

.no-resources-message {
  padding: 20px;
  text-align: center;
  color: #999;
  font-style: italic;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 1px dashed #e0e0e0;
}

/* Add Resources Button */
.add-resources-btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  margin-top: 16px;
  background-color: #374151;
  color: #ffffff;
  border: none;
  padding: 12px 20px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.add-resources-btn:hover {
  background-color: #4B5563;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Modal Styles */
.resources-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.resources-modal {
  background: #ffffff;
  border-radius: 12px;
  width: 90vw;
  max-width: 800px;
  max-height: 90vh;
  box-shadow: 0 5px 25px rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.modal-header {
  padding: 20px 24px;
  border-bottom: 1px solid #e0e0e0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: #FFF8EB;
}

.modal-title {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.modal-close-btn {
  background: none;
  border: none;
  font-size: 28px;
  line-height: 1;
  color: #666;
  cursor: pointer;
  transition: color 0.2s ease;
}

.modal-close-btn:hover {
  color: #333;
}

.modal-body {
  padding: 24px;
  overflow-y: auto;
  flex-grow: 1;
  background-color: #FFFBF3;
}

/* PDF Resource Upload Section */
.pdf-resource-upload-section {
  background-color: #ffffff;
  border-radius: 12px;
  border: 1px solid #e0e0e0;
  padding: 20px;
  margin-bottom: 20px;
}

.pdf-resource-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.pdf-resource-header h4 {
  font-size: 16px;
  font-weight: 600;
  color: #333;
  margin: 0;
}

.remove-resource-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: 1px solid #dc3545;
  border-radius: 6px;
  background-color: #f8d7da;
  color: #721c24;
  cursor: pointer;
  transition: all 0.2s ease;
}

.remove-resource-btn:hover {
  background-color: #dc3545;
  color: #ffffff;
}

/* File Input Groups */
.file-input-group {
  margin-bottom: 16px;
}

.file-input-group label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #333;
  margin-bottom: 8px;
}

.file-input-wrapper {
  position: relative;
}

.file-input {
  position: absolute;
  opacity: 0;
  width: 100%;
  height: 100%;
  cursor: pointer;
}

.text-input {
  width: 100%;
  padding: 12px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.2s ease;
}

.text-input:focus {
  outline: none;
  border-color: #374151;
}

/* File Preview Styles */
.file-preview {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  border: 2px dashed #e0e0e0;
  border-radius: 8px;
  background-color: #f8f9fa;
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 48px;
}

.file-preview:hover {
  border-color: #374151;
  background-color: #f1f3f4;
}

.file-preview-icon {
  display: flex;
  align-items: center;
  color: #666;
}

.file-name {
  flex: 1;
  font-size: 14px;
  color: #333;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.file-name-placeholder {
  flex: 1;
  font-size: 14px;
  color: #999;
  font-style: italic;
}

.file-clear-btn {
  background: none;
  border: none;
  font-size: 18px;
  color: #666;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.file-clear-btn:hover {
  background-color: #e9ecef;
  color: #333;
}

.audio-files-preview {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.help-text {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
}

/* Error Messages */
.error-message {
  color: #dc3545;
  font-size: 12px;
  margin-top: 4px;
  padding: 4px 8px;
  background-color: #f8d7da;
  border-radius: 4px;
  border: 1px solid #f5c6cb;
}

.general-error {
  margin-bottom: 16px;
  font-size: 14px;
  padding: 12px;
}

/* Add More Button */
.add-more-btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background-color: #f8f9fa;
  color: #374151;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
  margin-top: 8px;
}

.add-more-btn:hover {
  background-color: #e9ecef;
  border-color: #6c757d;
}

/* Modal Footer */
.modal-footer {
  padding: 16px 24px;
  border-top: 1px solid #e0e0e0;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  background-color: #FFF8EB;
}

.btn {
  padding: 10px 20px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-secondary {
  background-color: #f8f9fa;
  color: #6c757d;
  border-color: #6c757d;
}

.btn-secondary:hover:not(:disabled) {
  background-color: #e9ecef;
}

.btn-primary {
  background-color: #374151;
  color: #ffffff;
  border-color: #374151;
}

.btn-primary:hover:not(:disabled) {
  background-color: #4B5563;
  border-color: #4B5563;
}

/* Responsive Design - Admin Portal Breakpoints */
@media (max-width: 1024px) {
  .topics-header-card {
    padding: 20px 24px;
  }

  .topics-controls,
  .topics-list-container {
    padding-left: 0;
    padding-right: 0;
    margin: 24px 0;
  }

  .topics-page-title {
    font-size: 28px;
  }

  .topics-add-btn {
    padding: 10px 20px;
    font-size: 13px;
  }
}

@media (max-width: 768px) {
  .topics-header-card {
    padding: 16px 20px;
  }

  .topics-controls,
  .topics-list-container {
    padding-left: 0;
    padding-right: 0;
    margin: 20px 0;
  }

  .topics-page-title {
    font-size: 24px;
  }

  .topics-controls-row {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .topics-breadcrumb {
    text-align: center;
  }

  .topics-add-btn {
    width: 100%;
    justify-content: center;
  }

  .topic-card-header {
    padding: 16px 20px;
  }

  .topic-card-body {
    padding: 0 20px 20px 20px;
  }

  .topic-card-body.expanded {
    padding: 16px 20px 20px 20px;
  }

  .topic-card-actions {
    gap: 12px;
  }

  .pdf-resource-item {
    padding: 12px;
  }

  .audio-resource-item {
    padding: 10px 12px 10px 28px;
  }

  .resource-actions {
    gap: 6px;
  }

  .resource-action-btn {
    width: 28px;
    height: 28px;
  }

  .resources-modal {
    width: 95vw;
    max-height: 85vh;
  }

  .modal-header {
    padding: 16px 20px;
  }

  .modal-body {
    padding: 20px;
  }

  .pdf-resource-upload-section {
    padding: 16px;
  }
}

@media (max-width: 576px) {
  .topics-header-card {
    padding: 12px 16px;
  }

  .topics-controls,
  .topics-list-container {
    padding-left: 0;
    padding-right: 0;
    margin: 16px 0;
  }

  .topics-page-title {
    font-size: 20px;
  }

  .topics-breadcrumb span {
    font-size: 14px;
  }

  .topics-add-btn {
    padding: 10px 16px;
    font-size: 12px;
  }

  .topic-card-header {
    padding: 14px 16px;
  }

  .topic-card-body {
    padding: 0 16px 16px 16px;
  }

  .topic-card-title h3 {
    font-size: 18px;
  }

  .topic-edit-btn,
  .topic-delete-btn {
    width: 28px;
    height: 28px;
  }

  .topic-card-actions {
    gap: 8px;
  }

  .pdf-resource-item {
    padding: 10px;
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .audio-resource-item {
    padding: 8px 10px 8px 24px;
    flex-direction: column;
    align-items: flex-start;
    gap: 6px;
  }

  .resource-actions {
    align-self: flex-end;
    gap: 4px;
  }

  .resource-action-btn {
    width: 24px;
    height: 24px;
  }

  .resources-modal {
    width: 98vw;
    max-height: 80vh;
  }

  .modal-header {
    padding: 12px 16px;
  }

  .modal-title {
    font-size: 18px;
  }

  .modal-body {
    padding: 16px;
  }

  .pdf-resource-upload-section {
    padding: 12px;
  }

  .file-preview {
    padding: 8px;
    min-height: 40px;
  }

  .add-resources-btn {
    width: 100%;
    justify-content: center;
  }
}