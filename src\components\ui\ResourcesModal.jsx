import React, { useState } from 'react';
import Modal from 'react-modal';
import '../../styles/Topics.css'; // Ensure CSS is imported

// Helper component for file display
const FilePreview = ({ file, onClear }) => {
  if (!file) return <span className="file-name-placeholder">No file selected</span>;
  return (
    <div className="file-preview">
      <span className="file-name">{file.name}</span>
      <button onClick={onClear} className="file-clear-btn" title="Clear selection">
        ×
      </button>
    </div>
  );
};

const ResourcesModal = ({ isOpen, onClose, onSave, topicTitle }) => {
  // State for resource pairs { id, pdf, audio }
  const [resources, setResources] = useState([{ id: 1, pdf: null, audio: null }]);

  const handleFileChange = (id, fileType, file) => {
    setResources(currentResources =>
      currentResources.map(res => (res.id === id ? { ...res, [fileType]: file } : res))
    );
  };

  const handleClearFile = (id, fileType) => {
    // A little trick to reset the specific file input visually
    const fileInput = document.getElementById(`file-input-${fileType}-${id}`);
    if (fileInput) fileInput.value = '';

    handleFileChange(id, fileType, null);
  };


  const addResourceRow = () => {
    setResources(prev => [...prev, { id: Date.now(), pdf: null, audio: null }]);
  };

  const removeResourceRow = (id) => {
    setResources(prev => prev.filter(res => res.id !== id));
  };

  const handleSave = () => {
    // Filter out rows where no files were selected
    const validResources = resources.filter(res => res.pdf || res.audio);
    if (validResources.length > 0) {
      onSave(validResources);
    }
    onClose(); // Close modal after saving
  };

  return (
    <Modal
      isOpen={isOpen}
      onRequestClose={onClose}
      className="resources-modal"
      overlayClassName="resources-modal-overlay"
      ariaHideApp={true}
    >
      <div className="modal-header">
        <h2 className="modal-title">Add Resources to "{topicTitle}"</h2>
        <button onClick={onClose} className="modal-close-btn">×</button>
      </div>

      <div className="modal-body">
        {resources.map((resource, index) => (
          <div key={resource.id} className="resource-upload-row">
            <div className="file-input-group">
              <label htmlFor={`file-input-pdf-${resource.id}`}>PDF File</label>
              <div className="file-input-wrapper">
                <input
                  type="file"
                  id={`file-input-pdf-${resource.id}`}
                  className="file-input"
                  accept=".pdf"
                  onChange={(e) => handleFileChange(resource.id, 'pdf', e.target.files[0])}
                />
                <FilePreview file={resource.pdf} onClear={() => handleClearFile(resource.id, 'pdf')} />
              </div>
            </div>

            <div className="file-input-group">
              <label htmlFor={`file-input-audio-${resource.id}`}>Associated Audio File</label>
              <div className="file-input-wrapper">
                <input
                  type="file"
                  id={`file-input-audio-${resource.id}`}
                  className="file-input"
                  accept="audio/*"
                  onChange={(e) => handleFileChange(resource.id, 'audio', e.target.files[0])}
                />
                <FilePreview file={resource.audio} onClear={() => handleClearFile(resource.id, 'audio')} />
              </div>
            </div>

            {resources.length > 1 && (
               <button onClick={() => removeResourceRow(resource.id)} className="remove-row-btn">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"><polyline points="3,6 5,6 21,6"/><path d="m19,6v14a2,2 0 0,1 -2,2H7a2,2 0 0,1 -2,-2V6m3,0V4a2,2 0 0,1 2,-2h4a2,2 0 0,1 2,2v2"/></svg>
              </button>
            )}
          </div>
        ))}
        <button onClick={addResourceRow} className="add-more-btn">
          Add Another Resource
        </button>
      </div>

      <div className="modal-footer">
        <button onClick={onClose} className="btn btn-secondary">Cancel</button>
        <button onClick={handleSave} className="btn btn-primary">Save Resources</button>
      </div>
    </Modal>
  );
};

export default ResourcesModal;