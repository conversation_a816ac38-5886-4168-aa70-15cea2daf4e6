import React, { useState } from 'react';
import Modal from 'react-modal';
import '../../styles/Topics.css'; // Ensure CSS is imported

// Helper component for file display
const FilePreview = ({ file, onClear, type }) => {
  if (!file) return <span className="file-name-placeholder">No file selected</span>;

  const getFileIcon = () => {
    if (type === 'pdf') {
      return (
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
          <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2Z"/>
          <polyline points="14,2 14,8 20,8"/>
        </svg>
      );
    } else {
      return (
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
          <polygon points="11 5 6 9 2 9 2 15 6 15 11 19 11 5"/>
          <path d="M19.07 4.93a10 10 0 0 1 0 14.14M15.54 8.46a5 5 0 0 1 0 7.07"/>
        </svg>
      );
    }
  };

  return (
    <div className="file-preview">
      <div className="file-preview-icon">
        {getFileIcon()}
      </div>
      <span className="file-name">{file.name}</span>
      <button onClick={onClear} className="file-clear-btn" title="Clear selection">
        ×
      </button>
    </div>
  );
};

const ResourcesModal = ({ isOpen, onClose, onSave, topicTitle }) => {
  // State for PDF resources with associated audio files
  const [pdfResources, setPdfResources] = useState([{
    id: 1,
    pdf: null,
    audioFiles: [],
    title: ''
  }]);
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});

  const handlePdfChange = (id, file) => {
    setPdfResources(currentResources =>
      currentResources.map(res => (res.id === id ? { ...res, pdf: file } : res))
    );
    // Clear any existing errors for this PDF
    if (errors[`pdf-${id}`]) {
      setErrors(prev => ({ ...prev, [`pdf-${id}`]: null }));
    }
  };

  const handleTitleChange = (id, title) => {
    setPdfResources(currentResources =>
      currentResources.map(res => (res.id === id ? { ...res, title } : res))
    );
  };

  const handleAudioFilesChange = (id, files) => {
    const audioFilesArray = Array.from(files);
    setPdfResources(currentResources =>
      currentResources.map(res => (res.id === id ? { ...res, audioFiles: audioFilesArray } : res))
    );
  };

  const handleClearPdf = (id) => {
    const fileInput = document.getElementById(`pdf-input-${id}`);
    if (fileInput) fileInput.value = '';
    handlePdfChange(id, null);
  };

  const handleClearAudio = (id) => {
    const fileInput = document.getElementById(`audio-input-${id}`);
    if (fileInput) fileInput.value = '';
    handleAudioFilesChange(id, []);
  };

  const addPdfResource = () => {
    setPdfResources(prev => [...prev, {
      id: Date.now(),
      pdf: null,
      audioFiles: [],
      title: ''
    }]);
  };

  const removePdfResource = (id) => {
    setPdfResources(prev => prev.filter(res => res.id !== id));
    // Clear any errors for this resource
    setErrors(prev => {
      const newErrors = { ...prev };
      delete newErrors[`pdf-${id}`];
      return newErrors;
    });
  };

  const validateFiles = () => {
    const newErrors = {};
    let hasErrors = false;

    pdfResources.forEach(resource => {
      if (resource.pdf) {
        // Validate PDF file type
        if (resource.pdf.type !== 'application/pdf') {
          newErrors[`pdf-${resource.id}`] = 'Please select a valid PDF file';
          hasErrors = true;
        }
        // Validate PDF file size (10MB limit)
        else if (resource.pdf.size > 10 * 1024 * 1024) {
          newErrors[`pdf-${resource.id}`] = 'PDF file size must be less than 10MB';
          hasErrors = true;
        }

        // Validate audio files
        resource.audioFiles.forEach((audio, index) => {
          if (!audio.type.startsWith('audio/')) {
            newErrors[`audio-${resource.id}-${index}`] = 'Please select valid audio files';
            hasErrors = true;
          }
          // Validate audio file size (50MB limit)
          else if (audio.size > 50 * 1024 * 1024) {
            newErrors[`audio-${resource.id}-${index}`] = 'Audio file size must be less than 50MB';
            hasErrors = true;
          }
        });
      }
    });

    setErrors(newErrors);
    return !hasErrors;
  };

  const handleSave = async () => {
    // Filter out resources without PDF files
    const validResources = pdfResources.filter(res => res.pdf);

    if (validResources.length === 0) {
      setErrors({ general: 'Please select at least one PDF file' });
      return;
    }

    if (!validateFiles()) {
      return;
    }

    setLoading(true);
    try {
      // Transform data to match expected structure
      const formattedResources = validResources.map(resource => ({
        title: resource.title || resource.pdf.name.replace('.pdf', ''),
        filename: resource.pdf.name,
        size: resource.pdf.size,
        uploadDate: new Date().toISOString(),
        url: URL.createObjectURL(resource.pdf), // Temporary URL for preview
        audioFiles: resource.audioFiles.map(audio => ({
          title: audio.name.replace(/\.[^/.]+$/, ''),
          filename: audio.name,
          size: audio.size,
          duration: 0, // Would be calculated on server
          url: URL.createObjectURL(audio) // Temporary URL for preview
        }))
      }));

      await onSave(formattedResources);
      onClose();

      // Reset form
      setPdfResources([{ id: 1, pdf: null, audioFiles: [], title: '' }]);
      setErrors({});
    } catch (error) {
      setErrors({ general: 'Failed to save resources. Please try again.' });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      onRequestClose={onClose}
      className="resources-modal"
      overlayClassName="resources-modal-overlay"
      ariaHideApp={false}
    >
      <div className="modal-header">
        <h2 className="modal-title">Add Resources to "{topicTitle}"</h2>
        <button onClick={onClose} className="modal-close-btn">×</button>
      </div>

      <div className="modal-body">
        {errors.general && (
          <div className="error-message general-error">
            {errors.general}
          </div>
        )}

        {pdfResources.map((resource, index) => (
          <div key={resource.id} className="pdf-resource-upload-section">
            <div className="pdf-resource-header">
              <h4>PDF Resource {index + 1}</h4>
              {pdfResources.length > 1 && (
                <button
                  onClick={() => removePdfResource(resource.id)}
                  className="remove-resource-btn"
                  title="Remove this PDF resource"
                >
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <polyline points="3,6 5,6 21,6"/>
                    <path d="m19,6v14a2,2 0 0,1 -2,2H7a2,2 0 0,1 -2,-2V6m3,0V4a2,2 0 0,1 2,-2h4a2,2 0 0,1 2,2v2"/>
                  </svg>
                </button>
              )}
            </div>

            <div className="file-input-group">
              <label htmlFor={`pdf-input-${resource.id}`}>PDF File *</label>
              <div className="file-input-wrapper">
                <input
                  type="file"
                  id={`pdf-input-${resource.id}`}
                  className="file-input"
                  accept=".pdf"
                  onChange={(e) => handlePdfChange(resource.id, e.target.files[0])}
                />
                <FilePreview
                  file={resource.pdf}
                  onClear={() => handleClearPdf(resource.id)}
                  type="pdf"
                />
              </div>
              {errors[`pdf-${resource.id}`] && (
                <div className="error-message">
                  {errors[`pdf-${resource.id}`]}
                </div>
              )}
            </div>

            <div className="file-input-group">
              <label htmlFor={`title-input-${resource.id}`}>Resource Title (Optional)</label>
              <input
                type="text"
                id={`title-input-${resource.id}`}
                className="text-input"
                placeholder="Enter a custom title for this resource"
                value={resource.title}
                onChange={(e) => handleTitleChange(resource.id, e.target.value)}
              />
            </div>

            <div className="file-input-group">
              <label htmlFor={`audio-input-${resource.id}`}>Associated Audio Files (Optional)</label>
              <div className="file-input-wrapper">
                <input
                  type="file"
                  id={`audio-input-${resource.id}`}
                  className="file-input"
                  accept="audio/*"
                  multiple
                  onChange={(e) => handleAudioFilesChange(resource.id, e.target.files)}
                />
                <div className="audio-files-preview">
                  {resource.audioFiles.length > 0 ? (
                    resource.audioFiles.map((audio, audioIndex) => (
                      <FilePreview
                        key={audioIndex}
                        file={audio}
                        onClear={() => handleClearAudio(resource.id)}
                        type="audio"
                      />
                    ))
                  ) : (
                    <span className="file-name-placeholder">No audio files selected</span>
                  )}
                </div>
              </div>
              <div className="help-text">
                You can select multiple audio files to associate with this PDF
              </div>
            </div>
          </div>
        ))}

        <button onClick={addPdfResource} className="add-more-btn">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <line x1="12" y1="5" x2="12" y2="19"/>
            <line x1="5" y1="12" x2="19" y2="12"/>
          </svg>
          Add Another PDF Resource
        </button>
      </div>

      <div className="modal-footer">
        <button onClick={onClose} className="btn btn-secondary" disabled={loading}>
          Cancel
        </button>
        <button
          onClick={handleSave}
          className="btn btn-primary"
          disabled={loading}
        >
          {loading ? 'Saving...' : 'Save Resources'}
        </button>
      </div>
    </Modal>
  );
};

export default ResourcesModal;