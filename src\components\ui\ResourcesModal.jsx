import React, { useState } from 'react';
import Modal from 'react-modal';
import '../../styles/Topics.css'; // Ensure CSS is imported

// Helper component for file display
const FilePreview = ({ file, onClear, onRemove, type, showRemove = false }) => {
  if (!file) return <span className="file-name-placeholder">No file selected</span>;

  const getFileIcon = () => {
    if (type === 'pdf') {
      return (
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
          <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2Z"/>
          <polyline points="14,2 14,8 20,8"/>
        </svg>
      );
    } else {
      return (
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
          <polygon points="11 5 6 9 2 9 2 15 6 15 11 19 11 5"/>
          <path d="M19.07 4.93a10 10 0 0 1 0 14.14M15.54 8.46a5 5 0 0 1 0 7.07"/>
        </svg>
      );
    }
  };

  const handleRemoveClick = (e) => {
    e.preventDefault();
    e.stopPropagation();
    console.log('Remove button clicked', { showRemove, onRemove, onClear }); // Debug log

    if (showRemove && onRemove) {
      onRemove();
    } else if (onClear) {
      onClear();
    }
  };

  return (
    <div className="file-preview">
      <div className="file-preview-icon">
        {getFileIcon()}
      </div>
      <span className="file-name">{file.name}</span>
      <button
        type="button"
        onClick={handleRemoveClick}
        className="file-clear-btn"
        title={showRemove ? "Remove this file" : "Clear selection"}
      >
        ×
      </button>
    </div>
  );
};

const ResourcesModal = ({ isOpen, onClose, onSave, topicTitle, existingPdfs = [] }) => {
  // State for resource management
  const [resourceType, setResourceType] = useState('PDF');
  const [selectedFile, setSelectedFile] = useState(null);
  const [resourceTitle, setResourceTitle] = useState('');
  const [selectedParentPdf, setSelectedParentPdf] = useState('');
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});

  const handleFileChange = (file) => {
    setSelectedFile(file);
    // Auto-populate title if empty
    if (!resourceTitle && file) {
      const fileName = file.name.replace(/\.[^/.]+$/, ''); // Remove extension
      setResourceTitle(fileName);
    }
    // Clear any existing errors
    if (errors.file) {
      setErrors(prev => ({ ...prev, file: null }));
    }
  };

  const handleClearFile = () => {
    const fileInput = document.getElementById('resource-file-input');
    if (fileInput) fileInput.value = '';
    setSelectedFile(null);
  };

  const handleResourceTypeChange = (type) => {
    setResourceType(type);
    setSelectedFile(null);
    setResourceTitle('');
    setSelectedParentPdf('');
    setErrors({});
    // Clear file input
    const fileInput = document.getElementById('resource-file-input');
    if (fileInput) fileInput.value = '';
  };

  const validateResource = () => {
    const newErrors = {};
    let hasErrors = false;

    // Check if file is selected
    if (!selectedFile) {
      newErrors.file = 'Please select a file';
      hasErrors = true;
    } else {
      // Validate file type based on resource type
      if (resourceType === 'PDF' && selectedFile.type !== 'application/pdf') {
        newErrors.file = 'Please select a valid PDF file';
        hasErrors = true;
      } else if (resourceType === 'Audio' && !selectedFile.type.startsWith('audio/')) {
        newErrors.file = 'Please select a valid audio file';
        hasErrors = true;
      }

      // Validate file size
      const maxSize = resourceType === 'PDF' ? 10 * 1024 * 1024 : 50 * 1024 * 1024; // 10MB for PDF, 50MB for Audio
      if (selectedFile.size > maxSize) {
        const maxSizeText = resourceType === 'PDF' ? '10MB' : '50MB';
        newErrors.file = `File size must be less than ${maxSizeText}`;
        hasErrors = true;
      }
    }

    setErrors(newErrors);
    return !hasErrors;
  };

  const handleSave = async () => {
    if (!validateResource()) {
      return;
    }

    setLoading(true);
    try {
      // Create resource object based on type
      const resource = {
        type: resourceType,
        title: resourceTitle || selectedFile.name.replace(/\.[^/.]+$/, ''),
        filename: selectedFile.name,
        size: selectedFile.size,
        uploadDate: new Date().toISOString(),
        url: URL.createObjectURL(selectedFile), // Temporary URL for preview
      };

      // Add parent PDF ID for audio resources
      if (resourceType === 'Audio' && selectedParentPdf) {
        resource.parentPdfId = selectedParentPdf;
      }

      await onSave(resource);
      onClose();

      // Reset form
      setResourceType('PDF');
      setSelectedFile(null);
      setResourceTitle('');
      setSelectedParentPdf('');
      setErrors({});
    } catch (error) {
      setErrors({ general: 'Failed to save resource. Please try again.' });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal
      isOpen={isOpen}
      onRequestClose={onClose}
      className="resources-modal"
      overlayClassName="resources-modal-overlay"
      ariaHideApp={false}
    >
      <div className="modal-header">
        <h2 className="modal-title">Add Resources to "{topicTitle}"</h2>
        <button type="button" onClick={onClose} className="modal-close-btn">×</button>
      </div>

      <div className="modal-body">
        {errors.general && (
          <div className="error-message general-error">
            {errors.general}
          </div>
        )}

        {pdfResources.map((resource, index) => (
          <div key={resource.id} className="pdf-resource-upload-section">
            <div className="pdf-resource-header">
              <h4>PDF Resource {index + 1}</h4>
              {pdfResources.length > 1 && (
                <button
                  type="button"
                  onClick={() => removePdfResource(resource.id)}
                  className="remove-resource-btn"
                  title="Remove this PDF resource"
                >
                  <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                    <polyline points="3,6 5,6 21,6"/>
                    <path d="m19,6v14a2,2 0 0,1 -2,2H7a2,2 0 0,1 -2,-2V6m3,0V4a2,2 0 0,1 2,-2h4a2,2 0 0,1 2,2v2"/>
                  </svg>
                </button>
              )}
            </div>

            <div className="file-input-group">
              <label htmlFor={`pdf-input-${resource.id}`}>PDF File *</label>
              <div className="file-input-wrapper">
                <input
                  type="file"
                  id={`pdf-input-${resource.id}`}
                  className="file-input"
                  accept=".pdf"
                  onChange={(e) => handlePdfChange(resource.id, e.target.files[0])}
                />
                <label htmlFor={`pdf-input-${resource.id}`} className="file-input-label">
                  {resource.pdf ? (
                    <FilePreview
                      file={resource.pdf}
                      onClear={() => handleClearPdf(resource.id)}
                      type="pdf"
                    />
                  ) : (
                    <div className="file-drop-zone-small">
                      <div className="drop-zone-icon-small">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <circle cx="12" cy="12" r="10"/>
                          <line x1="12" y1="8" x2="12" y2="16"/>
                          <line x1="8" y1="12" x2="16" y2="12"/>
                        </svg>
                      </div>
                      <span className="file-name-placeholder">Click to select PDF file</span>
                    </div>
                  )}
                </label>
              </div>
              {errors[`pdf-${resource.id}`] && (
                <div className="error-message">
                  {errors[`pdf-${resource.id}`]}
                </div>
              )}
            </div>

            <div className="file-input-group">
              <label htmlFor={`title-input-${resource.id}`}>Resource Title (Optional)</label>
              <input
                type="text"
                id={`title-input-${resource.id}`}
                className="text-input"
                placeholder="Enter a custom title for this resource"
                value={resource.title}
                onChange={(e) => handleTitleChange(resource.id, e.target.value)}
              />
            </div>

            <div className="file-input-group">
              <label htmlFor={`audio-input-${resource.id}`}>Associated Audio Files (Optional)</label>
              <div className="file-input-wrapper">
                <input
                  type="file"
                  id={`audio-input-${resource.id}`}
                  className="file-input"
                  accept="audio/*"
                  multiple
                  onChange={(e) => handleAudioFilesChange(resource.id, e.target.files)}
                />
                <div className="audio-files-preview">
                  {resource.audioFiles.length > 0 ? (
                    <div className="selected-audio-files-container">
                      <div className="audio-files-header">
                        <span className="files-count">{resource.audioFiles.length} file(s) selected</span>
                        <button
                          type="button"
                          onClick={() => handleClearAudio(resource.id)}
                          className="clear-all-btn"
                          title="Clear all audio files"
                        >
                          Clear All
                        </button>
                      </div>
                      <div className="selected-audio-files-list">
                        {resource.audioFiles.map((audio, audioIndex) => (
                          <div key={audioIndex} className="audio-file-item-with-remove">
                            <div className="audio-file-preview">
                              <div className="file-preview-icon">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                  <polygon points="11 5 6 9 2 9 2 15 6 15 11 19 11 5"/>
                                  <path d="M19.07 4.93a10 10 0 0 1 0 14.14M15.54 8.46a5 5 0 0 1 0 7.07"/>
                                </svg>
                              </div>
                              <span className="file-name">{audio.name}</span>
                            </div>
                            <button
                              type="button"
                              onClick={() => handleRemoveAudioFile(resource.id, audioIndex)}
                              className="file-clear-btn"
                              title="Remove this file"
                            >
                              ×
                            </button>
                          </div>
                        ))}
                      </div>
                      <label htmlFor={`audio-input-${resource.id}`} className="add-more-audio-zone">
                        <div className="add-more-icon">
                          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                            <circle cx="12" cy="12" r="10"/>
                            <line x1="12" y1="8" x2="12" y2="16"/>
                            <line x1="8" y1="12" x2="16" y2="12"/>
                          </svg>
                        </div>
                        <span className="add-more-text">Add more audio files</span>
                      </label>
                    </div>
                  ) : (
                    <label htmlFor={`audio-input-${resource.id}`} className="file-drop-zone-small">
                      <div className="drop-zone-icon-small">
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <circle cx="12" cy="12" r="10"/>
                          <line x1="12" y1="8" x2="12" y2="16"/>
                          <line x1="8" y1="12" x2="16" y2="12"/>
                        </svg>
                      </div>
                      <span className="file-name-placeholder">Click to select audio files</span>
                    </label>
                  )}
                </div>
              </div>
              <div className="help-text">
                You can select multiple audio files to associate with this PDF
              </div>
            </div>
          </div>
        ))}

        <button type="button" onClick={addPdfResource} className="add-more-btn">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
            <circle cx="12" cy="12" r="10"/>
            <line x1="12" y1="8" x2="12" y2="16"/>
            <line x1="8" y1="12" x2="16" y2="12"/>
          </svg>
          Add Another PDF Resource
        </button>
      </div>

      <div className="modal-footer">
        <button type="button" onClick={onClose} className="btn btn-secondary" disabled={loading}>
          Cancel
        </button>
        <button
          type="button"
          onClick={handleSave}
          className="btn btn-primary"
          disabled={loading}
        >
          {loading ? 'Saving...' : 'Save Resources'}
        </button>
      </div>
    </Modal>
  );
};

export default ResourcesModal;