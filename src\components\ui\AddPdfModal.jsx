import React, { useState } from 'react';
import Modal from 'react-modal';
import '../../styles/Topics.css';

const AddPdfModal = ({ isOpen, onClose, onSave, topicTitle }) => {
  const [selectedFiles, setSelectedFiles] = useState([]);
  const [pdfTitles, setPdfTitles] = useState({});
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState({});

  const handleFilesChange = (files) => {
    if (files && files.length > 0) {
      const pdfFiles = Array.from(files).filter(file => file.type === 'application/pdf');
      const invalidFiles = Array.from(files).filter(file => file.type !== 'application/pdf');

      if (invalidFiles.length > 0) {
        setErrors({
          files: `${invalidFiles.length} file(s) skipped - only PDF files are allowed`
        });
      } else {
        setErrors({});
      }

      // Check file sizes (10MB limit per PDF)
      const oversizedFiles = pdfFiles.filter(file => file.size > 10 * 1024 * 1024);
      if (oversizedFiles.length > 0) {
        setErrors({
          files: `${oversizedFiles.length} file(s) are too large - PDF files must be less than 10MB each`
        });
        return;
      }

      setSelectedFiles(prevFiles => [...prevFiles, ...pdfFiles]);
    }

    // Clear the file input to allow selecting the same files again
    const fileInput = document.getElementById('pdf-files-input');
    if (fileInput) fileInput.value = '';
  };

  const handleRemoveFile = (index) => {
    const fileToRemove = selectedFiles[index];
    setSelectedFiles(prevFiles => prevFiles.filter((_, i) => i !== index));
    // Remove title for this file
    setPdfTitles(prevTitles => {
      const newTitles = { ...prevTitles };
      delete newTitles[fileToRemove.name];
      return newTitles;
    });
    if (errors.files) {
      setErrors({});
    }
  };

  const handleClearAll = () => {
    setSelectedFiles([]);
    setPdfTitles({});
    setErrors({});
  };

  const handleTitleChange = (fileName, title) => {
    setPdfTitles(prevTitles => ({
      ...prevTitles,
      [fileName]: title
    }));
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const handleSave = async () => {
    if (selectedFiles.length === 0) {
      setErrors({ files: 'Please select at least one PDF file' });
      return;
    }

    setLoading(true);
    try {
      // Transform files to PDF resources
      const pdfResources = selectedFiles.map(file => ({
        id: Date.now() + Math.random(), // Temporary ID
        title: pdfTitles[file.name] || file.name.replace('.pdf', ''),
        filename: file.name,
        size: file.size,
        uploadDate: new Date().toISOString(),
        url: URL.createObjectURL(file), // Temporary URL for preview
        audioFiles: []
      }));

      await onSave(pdfResources);
      onClose();

      // Reset form
      setSelectedFiles([]);
      setPdfTitles({});
      setErrors({});
    } catch (error) {
      setErrors({ general: 'Failed to add PDF files. Please try again.' });
    } finally {
      setLoading(false);
    }
  };

  const handleClose = () => {
    onClose();
    // Reset form
    setSelectedFiles([]);
    setPdfTitles({});
    setErrors({});
  };

  return (
    <Modal
      isOpen={isOpen}
      onRequestClose={handleClose}
      className="resources-modal pdf-upload-modal"
      overlayClassName="resources-modal-overlay"
      ariaHideApp={false}
    >
      <div className="modal-header">
        <h2 className="modal-title">Add PDF Files to "{topicTitle}"</h2>
        <button onClick={handleClose} className="modal-close-btn">×</button>
      </div>

      <div className="modal-body">
        {errors.general && (
          <div className="error-message general-error">
            {errors.general}
          </div>
        )}

        {/* File Upload Section */}
        <div className="file-input-group">
          <label htmlFor="pdf-files-input">Select PDF Files *</label>
          <div className="file-input-wrapper">
            <input
              type="file"
              id="pdf-files-input"
              className="file-input"
              accept=".pdf"
              multiple
              onChange={(e) => handleFilesChange(e.target.files)}
            />
            <label htmlFor="pdf-files-input" className="file-input-label">
              <div className="pdf-upload-zone">
                {selectedFiles.length > 0 ? (
                  <div className="selected-pdfs-container">
                    <div className="selected-pdfs-header">
                      <span className="files-count">{selectedFiles.length} PDF file(s) selected</span>
                      <button
                        onClick={handleClearAll}
                        className="clear-all-btn"
                        title="Clear all files"
                        type="button"
                      >
                        Clear All
                      </button>
                    </div>
                    <div className="selected-pdfs-list">
                      {selectedFiles.map((file, index) => (
                        <div key={index} className="pdf-file-item-with-title">
                          <div className="pdf-file-header">
                            <div className="pdf-file-icon">
                              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                                <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2Z"/>
                                <polyline points="14,2 14,8 20,8"/>
                              </svg>
                            </div>
                            <div className="pdf-file-info">
                              <div className="pdf-file-name">{file.name}</div>
                              <div className="pdf-file-size">{formatFileSize(file.size)}</div>
                            </div>
                            <button
                              onClick={() => handleRemoveFile(index)}
                              className="pdf-file-remove-btn"
                              title="Remove this file"
                              type="button"
                            >
                              ×
                            </button>
                          </div>
                          <div className="pdf-title-input-group">
                            <input
                              type="text"
                              className="pdf-title-input"
                              placeholder={`Title for ${file.name.replace('.pdf', '')}`}
                              value={pdfTitles[file.name] || ''}
                              onChange={(e) => handleTitleChange(file.name, e.target.value)}
                            />
                          </div>
                        </div>
                      ))}
                    </div>
                    <label htmlFor="pdf-files-input" className="add-more-pdfs-zone">
                      <div className="add-more-icon">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                          <circle cx="12" cy="12" r="10"/>
                          <line x1="12" y1="8" x2="12" y2="16"/>
                          <line x1="8" y1="12" x2="16" y2="12"/>
                        </svg>
                      </div>
                      <span className="add-more-text">Add more PDF files</span>
                    </label>
                  </div>
                ) : (
                  <div className="file-drop-zone">
                    <div className="drop-zone-icon">
                      <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                        <path d="M14,2H6A2,2 0 0,0 4,4V20A2,2 0 0,0 6,22H18A2,2 0 0,0 20,20V8L14,2Z"/>
                        <polyline points="14,2 14,8 20,8"/>
                      </svg>
                    </div>
                    <div className="drop-zone-text">
                      <p><strong>Click to select PDF files</strong></p>
                      <p>or drag and drop them here</p>
                      <p className="file-types">You can select multiple PDF files at once</p>
                    </div>
                  </div>
                )}
              </div>
            </label>
          </div>
          {errors.files && (
            <div className="error-message">
              {errors.files}
            </div>
          )}
          <div className="help-text">
            Select one or more PDF files. Each file should be less than 10MB. You can add audio files to each PDF later using the + button.
          </div>
        </div>
      </div>

      <div className="modal-footer">
        <button onClick={handleClose} className="btn btn-secondary" disabled={loading}>
          Cancel
        </button>
        <button
          onClick={handleSave}
          className="btn btn-primary"
          disabled={loading || selectedFiles.length === 0}
        >
          {loading ? 'Adding...' : `Add ${selectedFiles.length} PDF${selectedFiles.length !== 1 ? 's' : ''}`}
        </button>
      </div>
    </Modal>
  );
};

export default AddPdfModal;
