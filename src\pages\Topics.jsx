import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useAuthStore } from '../stores/authStore';
import { topicsAPI, coursesAPI } from '../services/api';
import SideNavBar from '../components/layout/SideNavBar';
import TopicCard from '../components/ui/TopicCard'; // New component
import { toast } from 'react-toastify';
import '../styles/Topics.css';

const Topics = () => {
  const navigate = useNavigate();
  const { courseId } = useParams();
  const { currentUser, logout } = useAuthStore();
  const [topics, setTopics] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [course, setCourse] = useState(null);

  // Fetch course details and topics
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Fetch course details
        const coursesResponse = await coursesAPI.getAll();
        const currentCourse = coursesResponse.data?.find(course => course.id === courseId);

        if (!currentCourse) {
          throw new Error('Course not found');
        }

        setCourse({
          id: currentCourse.id,
          title: currentCourse.title,
          description: currentCourse.description || 'No description available'
        });

        // Fetch topics for the course
        const topicsResponse = await topicsAPI.getByCourseId(courseId);

        const formattedTopics = topicsResponse.data.data.map(topic => ({
          id: topic.id,
          title: topic.title,
          order: topic.orderIndex || 0,
          status: topic.status?.toLowerCase() || 'draft',
          description: topic.description || '',
          resources: {
            pdfs: [
              {
                title: 'Introduction to Structural Design',
                filename: 'introduction.pdf',
                size: 1258291, // bytes
                uploadDate: '2023-01-15T10:30:00Z',
                url: '/api/files/pdf/intro.pdf',
                audioFiles: [
                  {
                    title: 'Introduction Lecture Part 1',
                    filename: 'intro_part1.mp3',
                    size: 15728640, // bytes
                    duration: 1800, // seconds
                    url: '/api/files/audio/intro_part1.mp3'
                  },
                  {
                    title: 'Introduction Lecture Part 2',
                    filename: 'intro_part2.mp3',
                    size: 12582912, // bytes
                    duration: 1440, // seconds
                    url: '/api/files/audio/intro_part2.mp3'
                  },
                  {
                    title: 'Introduction Summary',
                    filename: 'intro_summary.mp3',
                    size: 8388608, // bytes
                    duration: 960, // seconds (16 minutes)
                    url: '/api/files/audio/intro_summary.mp3'
                  }
                ]
              },
              {
                title: 'Advanced Concepts',
                filename: 'advanced_concepts.pdf',
                size: 3565158, // bytes
                uploadDate: '2023-01-20T14:15:00Z',
                url: '/api/files/pdf/advanced.pdf',
                audioFiles: [
                  {
                    title: 'Advanced Concepts Overview',
                    filename: 'advanced_overview.mp3',
                    size: 18874368, // bytes
                    duration: 2160, // seconds (36 minutes)
                    url: '/api/files/audio/advanced_overview.mp3'
                  }
                ]
              },
              {
                title: 'Practical Examples',
                filename: 'practical_examples.pdf',
                size: 2847392, // bytes
                uploadDate: '2023-01-25T09:30:00Z',
                url: '/api/files/pdf/practical.pdf',
                audioFiles: [
                  {
                    title: 'Example 1 Walkthrough',
                    filename: 'example1_walkthrough.mp3',
                    size: 12582912, // bytes
                    duration: 1440, // seconds (24 minutes)
                    url: '/api/files/audio/example1.mp3'
                  },
                  {
                    title: 'Example 2 Analysis',
                    filename: 'example2_analysis.mp3',
                    size: 9437184, // bytes
                    duration: 1080, // seconds (18 minutes)
                    url: '/api/files/audio/example2.mp3'
                  },
                  {
                    title: 'Common Mistakes Discussion',
                    filename: 'common_mistakes.mp3',
                    size: 7340032, // bytes
                    duration: 840, // seconds (14 minutes)
                    url: '/api/files/audio/mistakes.mp3'
                  }
                ]
              },
              {
                title: 'Reference Materials',
                filename: 'reference_materials.pdf',
                size: 1572864, // bytes
                uploadDate: '2023-01-30T16:45:00Z',
                url: '/api/files/pdf/reference.pdf',
                audioFiles: []
              }
            ]
          },
        }));
        setTopics(formattedTopics);

      } catch (error) {
        console.error('Error fetching data:', error);
        const errorMessage = error.response?.data?.message || error.message || 'Failed to load course data';
        setError(errorMessage);
        toast.error(`Error: ${errorMessage}`);
      } finally {
        setLoading(false);
      }
    };

    if (courseId) {
      fetchData();
    }
  }, [courseId]);

  const handleTabChange = (tabId) => {
    // Navigation logic
  };

  const handleLogout = async () => {
    // Logout logic
  };

  const handleAddTopic = async () => {
    try {
      const newTopic = {
        courseId,
        title: `New Topic ${topics.length + 1}`,
        description: 'A default description for the new topic.',
        order: topics.length + 1,
        status: 'draft',
      };

      const response = await topicsAPI.create(newTopic);
      if (response.data) {
        navigate(`/course/${courseId}/topics/${response.data.id}/edit`);
        toast.success('New topic added successfully!');
      }

    } catch (error) {
      toast.error(error.response?.data?.message || 'Failed to add topic');
    }
  };

  const handleEditTopic = (topicId) => {
    navigate(`/course/${courseId}/topics/${topicId}/edit`);
  };

  const handleDeleteTopic = async (topicId) => {
    if (window.confirm('Are you sure you want to delete this topic?')) {
      try {
        await topicsAPI.delete(topicId);
        setTopics(topics.filter(t => t.id !== topicId));
        toast.success('Topic deleted successfully');
      } catch (error) {
        toast.error('Failed to delete topic.');
      }
    }
  };

  const handleResourcesUpdate = async (topicId, newResources, pdfIndex = null, audioFiles = null) => {
    try {
      // Here you would typically make an API call to save the resources
      // For now, we'll update the local state

      if (audioFiles && pdfIndex !== null) {
        // Adding audio files to existing PDF
        setTopics(prevTopics =>
          prevTopics.map(topic =>
            topic.id === topicId
              ? {
                  ...topic,
                  resources: {
                    ...topic.resources,
                    pdfs: topic.resources.pdfs.map((pdf, index) =>
                      index === pdfIndex
                        ? {
                            ...pdf,
                            audioFiles: [...(pdf.audioFiles || []), ...audioFiles]
                          }
                        : pdf
                    )
                  }
                }
              : topic
          )
        );
        toast.success(`Added ${audioFiles.length} audio file${audioFiles.length !== 1 ? 's' : ''} successfully!`);
      } else if (newResources) {
        // Adding new PDF resources
        setTopics(prevTopics =>
          prevTopics.map(topic =>
            topic.id === topicId
              ? {
                  ...topic,
                  resources: {
                    pdfs: [...(topic.resources?.pdfs || []), ...newResources]
                  }
                }
              : topic
          )
        );
        toast.success('Resources added successfully!');
      }
    } catch (error) {
      toast.error('Failed to add resources. Please try again.');
    }
  };

  return (
    <div className="dashboard-container">
      <SideNavBar
        activeTab="courses"
        onTabChange={handleTabChange}
        onLogout={handleLogout}
        currentUser={currentUser}
      />
      <div className="dashboard-main-content">
        <div className="topics-page-container">
          <div className="topics-header-card">
            <h1 className="topics-page-title">{course ? course.title : 'Loading...'}</h1>
          </div>

          <div className="topics-controls">
            <div className="topics-controls-row">
              <div className="topics-breadcrumb">
                <span onClick={() => navigate('/course')}>Courses</span>
                <span className="breadcrumb-separator">/</span>
                <span>Topics</span>
              </div>
              <button className="topics-add-btn" onClick={handleAddTopic}>
                Add New Topic
              </button>
            </div>
          </div>

          <div className="topics-list-container">
            {loading ? (
              <p>Loading topics...</p>
            ) : error ? (
              <p>Error: {error}</p>
            ) : (
              topics.map(topic => (
                <TopicCard
                  key={topic.id}
                  topic={topic}
                  onEdit={handleEditTopic}
                  onDelete={handleDeleteTopic}
                  onResourcesUpdate={handleResourcesUpdate}
                />
              ))
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Topics;