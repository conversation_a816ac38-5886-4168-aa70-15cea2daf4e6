import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { useAuthStore } from '../stores/authStore';
import { topicsAPI, coursesAPI } from '../services/api';
import SideNavBar from '../components/layout/SideNavBar';
import TopicCard from '../components/ui/TopicCard'; // New component
import { toast } from 'react-toastify';
import '../styles/Topics.css';

const Topics = () => {
  const navigate = useNavigate();
  const { courseId } = useParams();
  const { currentUser, logout } = useAuthStore();
  const [topics, setTopics] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [course, setCourse] = useState(null);

  // Fetch course details and topics
  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Fetch course details
        const coursesResponse = await coursesAPI.getAll();
        const currentCourse = coursesResponse.data?.find(course => course.id === courseId);

        if (!currentCourse) {
          throw new Error('Course not found');
        }

        setCourse({
          id: currentCourse.id,
          title: currentCourse.title,
          description: currentCourse.description || 'No description available'
        });

        // Fetch topics for the course
        const topicsResponse = await topicsAPI.getByCourseId(courseId);
        
        const formattedTopics = topicsResponse.data.data.map(topic => ({
          id: topic.id,
          title: topic.title,
          order: topic.orderIndex || 0,
          status: topic.status?.toLowerCase() || 'draft',
          description: topic.description || '',
          resources: { // Dummy data for resources; replace with actual data from API
            pdfs: [
              { name: 'Introduction.pdf', size: '1.2 MB', date: '2023-01-15' },
              { name: 'Advanced Concepts.pdf', size: '3.4 MB', date: '2023-01-20' },
            ],
            audios: [
              { name: 'Lecture 1.mp3', size: '15.6 MB', date: '2023-01-16' },
            ],
          },
        }));
        setTopics(formattedTopics);

      } catch (error) {
        console.error('Error fetching data:', error);
        const errorMessage = error.response?.data?.message || error.message || 'Failed to load course data';
        setError(errorMessage);
        toast.error(`Error: ${errorMessage}`);
      } finally {
        setLoading(false);
      }
    };

    if (courseId) {
      fetchData();
    }
  }, [courseId]);

  const handleTabChange = (tabId) => {
    // Navigation logic
  };

  const handleLogout = async () => {
    // Logout logic
  };

  const handleAddTopic = async () => {
    try {
      const newTopic = {
        courseId,
        title: `New Topic ${topics.length + 1}`,
        description: 'A default description for the new topic.',
        order: topics.length + 1,
        status: 'draft',
      };
      
      const response = await topicsAPI.create(newTopic);
      if (response.data) {
        navigate(`/course/${courseId}/topics/${response.data.id}/edit`);
        toast.success('New topic added successfully!');
      }

    } catch (error) {
      toast.error(error.response?.data?.message || 'Failed to add topic');
    }
  };

  const handleEditTopic = (topicId) => {
    navigate(`/course/${courseId}/topics/${topicId}/edit`);
  };

  const handleDeleteTopic = async (topicId) => {
    if (window.confirm('Are you sure you want to delete this topic?')) {
      try {
        await topicsAPI.delete(topicId);
        setTopics(topics.filter(t => t.id !== topicId));
        toast.success('Topic deleted successfully');
      } catch (error) {
        toast.error('Failed to delete topic.');
      }
    }
  };

  return (
    <div className="dashboard-container">
      <SideNavBar
        activeTab="courses"
        onTabChange={handleTabChange}
        onLogout={handleLogout}
        currentUser={currentUser}
      />
      <div className="dashboard-main-content">
        <div className="topics-page-container">
          <div className="topics-header-card">
            <h1 className="topics-page-title">{course ? course.title : 'Loading...'}</h1>
          </div>

          <div className="topics-controls">
            <div className="topics-controls-row">
              <div className="topics-breadcrumb">
                <span onClick={() => navigate('/course')}>Courses</span>
                <span className="breadcrumb-separator">/</span>
                <span>Topics</span>
              </div>
              <button className="topics-add-btn" onClick={handleAddTopic}>
                Add New Topic
              </button>
            </div>
          </div>
          
          <div className="topics-list-container">
            {loading ? (
              <p>Loading topics...</p>
            ) : error ? (
              <p>Error: {error}</p>
            ) : (
              topics.map(topic => (
                <TopicCard 
                  key={topic.id}
                  topic={topic}
                  onEdit={handleEditTopic}
                  onDelete={handleDeleteTopic}
                />
              ))
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Topics;